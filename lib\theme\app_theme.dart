import 'package:flutter/material.dart';

class AppColors {
  // Modern, clean primary colors - using a beautiful blue palette
  static const Color primary = Color(0xFF2563EB); // Modern blue
  static const Color primaryLight = Color(0xFF3B82F6);
  static const Color primaryDark = Color(0xFF1D4ED8);

  // Fresh accent colors - using a nice green
  static const Color accent = Color(0xFF059669); // Modern emerald
  static const Color accentLight = Color(0xFF10B981);
  static const Color accentDark = Color(0xFF047857);

  // Clean status colors
  static const Color success = Color(0xFF16A34A); // Fresh green
  static const Color error = Color(0xFFEF4444); // Clean red
  static const Color warning = Color(0xFFF59E0B); // Warm amber

  // Beautiful text colors
  static const Color textPrimary = Color(0xFF111827); // Rich dark gray
  static const Color textSecondary = Color(0xFF6B7280); // Medium gray
  static const Color textLight = Color(0xFF9CA3AF); // Light gray
  static const Color textDark = Colors.white; // White for dark mode text

  // Clean backgrounds
  static const Color backgroundLight = Colors.white; // Pure white background
  static const Color backgroundDark = Color(0xFF121212); // Deeper dark background

  // Surface colors
  static const Color surfaceLight = Colors.white; // Pure white
  static const Color surfaceDark = Color(0xFF1E1E1E); // Slightly lighter dark surface

  // Subtle borders
  static const Color borderLight = Color(0xFFE5E7EB); // Very light gray
  static const Color borderDark = Color(0xFF374151); // Clean dark border
  
  // Container colors
  static const Color tertiaryContainerLight = Color(0xFFDCFCE7); // Light green tint
  static const Color tertiaryContainerDark = Color(0xFF064E3B); // Dark green tint

  // Neutral colors for general UI elements
  static const Color neutralLight = Color(0xFFF3F4F6); // Very light gray for backgrounds
  static const Color neutralDark = Color(0xFF2D3748); // Dark gray for backgrounds

  // Beautiful category colors - much more appealing!
  static const Color categoryEntertainment = Color(0xFFEC4899); // Beautiful pink
  static const Color categoryStreaming = Color(0xFFF97316); // Vibrant orange
  static const Color categoryMusic = Color(0xFF8B5CF6); // Lovely purple
  static const Color categorySoftware = Color(0xFF3B82F6); // Clean blue
  static const Color categoryFitness = Color(0xFF22C55E); // Fresh green
  static const Color categoryNews = Color(0xFF06B6D4); // Modern cyan
  static const Color categoryFinance = Color(0xFFF59E0B); // Gold amber
  static const Color categoryProductivity = Color(0xFF6366F1); // Indigo
  static const Color categoryEducation = Color(0xFFEAB308); // Warm yellow
  static const Color categoryFoodDining = Color(0xFFEF4444); // Appetizing red
  static const Color categoryHealth = Color(0xFF10B981); // Healthy green
  static const Color categoryOther = Color(0xFF6B7280); // Neutral gray
}

class AppTheme {
  static final ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: AppColors.primary,
    scaffoldBackgroundColor: AppColors.backgroundLight,
    cardColor: AppColors.surfaceLight,

    colorScheme: const ColorScheme.light(
      primary: AppColors.primary,
      onPrimary: Colors.white,
      secondary: AppColors.accent,
      onSecondary: Colors.white,
      error: AppColors.error,
      onError: Colors.white,
      surface: AppColors.surfaceLight,
      onSurface: AppColors.textPrimary,
      surfaceContainerHighest: AppColors.borderLight,
      outline: AppColors.borderLight,
      onSurfaceVariant: AppColors.textSecondary,
      tertiary: AppColors.success,
      onTertiary: Colors.white,
      tertiaryContainer: AppColors.tertiaryContainerLight,
      onTertiaryContainer: AppColors.success,
      shadow: Color(0x0A000000), // Very subtle shadow
    ),

    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.backgroundLight, // Use backgroundLight
      foregroundColor: AppColors.textPrimary,
      elevation: 0,
      centerTitle: true,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        color: AppColors.textPrimary,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    cardTheme: CardThemeData(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: AppColors.surfaceLight,
      shadowColor: const Color(0x0A000000),
      surfaceTintColor: Colors.transparent,
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        elevation: 0,
        shadowColor: Colors.transparent,
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        textStyle: const TextStyle(fontWeight: FontWeight.w600),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),

    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.neutralLight, // Use neutralLight
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderLight),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderLight),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error, width: 2),
      ),
      labelStyle: const TextStyle(color: AppColors.textSecondary),
      hintStyle: const TextStyle(color: AppColors.textLight),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    ),

    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.textSecondary,
      backgroundColor: AppColors.backgroundLight, // Use backgroundLight
      elevation: 8,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
      unselectedLabelStyle: TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
    ),

    // Clean, readable text themes
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w700,
        fontSize: 32,
        height: 1.2,
      ),
      headlineMedium: TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w700,
        fontSize: 28,
        height: 1.3,
      ),
      headlineSmall: TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 24,
        height: 1.3,
      ),
      titleLarge: TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 22,
        height: 1.4,
      ),
      titleMedium: TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 16,
        height: 1.4,
      ),
      titleSmall: TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 14,
        height: 1.4,
      ),
      bodyLarge: TextStyle(
        color: AppColors.textPrimary,
        fontSize: 16,
        height: 1.5,
      ),
      bodyMedium: TextStyle(
        color: AppColors.textSecondary,
        fontSize: 14,
        height: 1.5,
      ),
      bodySmall: TextStyle(
        color: AppColors.textLight,
        fontSize: 12,
        height: 1.4,
      ),
      labelLarge: TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 14,
      ),
      labelMedium: TextStyle(
        color: AppColors.textSecondary,
        fontWeight: FontWeight.w500,
        fontSize: 12,
      ),
      labelSmall: TextStyle(
        color: AppColors.textLight,
        fontWeight: FontWeight.w500,
        fontSize: 11,
      ),
    ),
  );

  static final ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: AppColors.primaryLight,
    scaffoldBackgroundColor: AppColors.backgroundDark,
    cardColor: AppColors.surfaceDark,

    colorScheme: const ColorScheme.dark(
      primary: AppColors.primaryLight,
      onPrimary: AppColors.textDark, // Changed to textDark (white)
      secondary: AppColors.accentLight,
      onSecondary: AppColors.textDark, // Changed to textDark (white)
      error: AppColors.error,
      onError: Colors.white,
      surface: AppColors.surfaceDark,
      onSurface: AppColors.textDark, // Changed to textDark (white)
      surfaceContainerHighest: AppColors.borderDark,
      outline: AppColors.borderDark,
      onSurfaceVariant: AppColors.textLight,
      tertiary: AppColors.success,
      onTertiary: AppColors.textDark, // Changed to textDark (white)
      tertiaryContainer: AppColors.tertiaryContainerDark,
      onTertiaryContainer: AppColors.accentLight,
      shadow: Color(0x33000000),
    ),

    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.backgroundDark, // Use backgroundDark
      foregroundColor: AppColors.textDark, // Changed to textDark (white)
      elevation: 0,
      centerTitle: true,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    cardTheme: CardThemeData(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: AppColors.surfaceDark,
      shadowColor: const Color(0x33000000),
      surfaceTintColor: Colors.transparent,
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryLight,
        foregroundColor: AppColors.textDark, // Changed to textDark (white)
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        elevation: 0,
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primaryLight,
        textStyle: const TextStyle(fontWeight: FontWeight.w600),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),

    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.neutralDark, // Use neutralDark
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderDark),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderDark),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.primaryLight, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error, width: 2),
      ),
      labelStyle: const TextStyle(color: AppColors.textLight),
      hintStyle: const TextStyle(color: AppColors.textSecondary),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    ),

    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      selectedItemColor: AppColors.primaryLight,
      unselectedItemColor: AppColors.textLight,
      backgroundColor: AppColors.surfaceDark, // Use surfaceDark
      elevation: 8,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
      unselectedLabelStyle: TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
    ),

    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontWeight: FontWeight.w700,
        fontSize: 32,
        height: 1.2,
      ),
      headlineMedium: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontWeight: FontWeight.w700,
        fontSize: 28,
        height: 1.3,
      ),
      headlineSmall: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontWeight: FontWeight.w600,
        fontSize: 24,
        height: 1.3,
      ),
      titleLarge: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontWeight: FontWeight.w600,
        fontSize: 22,
        height: 1.4,
      ),
      titleMedium: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontWeight: FontWeight.w600,
        fontSize: 16,
        height: 1.4,
      ),
      titleSmall: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontWeight: FontWeight.w500,
        fontSize: 14,
        height: 1.4,
      ),
      bodyLarge: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontSize: 16,
        height: 1.5,
      ),
      bodyMedium: TextStyle(
        color: AppColors.textLight,
        fontSize: 14,
        height: 1.5,
      ),
      bodySmall: TextStyle(
        color: AppColors.textSecondary,
        fontSize: 12,
        height: 1.4,
      ),
      labelLarge: TextStyle(
        color: AppColors.textDark, // Changed to textDark (white)
        fontWeight: FontWeight.w600,
        fontSize: 14,
      ),
      labelMedium: TextStyle(
        color: AppColors.textLight,
        fontWeight: FontWeight.w500,
        fontSize: 12,
      ),
      labelSmall: TextStyle(
        color: AppColors.textSecondary,
        fontWeight: FontWeight.w500,
        fontSize: 11,
      ),
    ),
  );
}