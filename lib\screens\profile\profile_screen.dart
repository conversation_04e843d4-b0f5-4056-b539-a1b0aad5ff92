import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'package:notrail/theme/theme_manager.dart';
import 'package:notrail/theme/app_theme.dart'; // Import AppTheme for AppColors

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  Future<void> _signOut(BuildContext context) async {
    try {
      await FirebaseAuth.instance.signOut();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Signed out successfully!'),
            backgroundColor: Theme.of(
              context,
            ).colorScheme.secondary, // Use theme color
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error signing out: $e'),
            backgroundColor: Theme.of(
              context,
            ).colorScheme.error, // Use theme color
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  void _showThemeSelection(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Select Theme',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface, // Use theme color
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildThemeOption(
                context,
                'System Default',
                ThemeMode.system,
                Icons.brightness_auto,
              ),
              _buildThemeOption(
                context,
                'Light Theme',
                ThemeMode.light,
                Icons.light_mode,
              ),
              _buildThemeOption(
                context,
                'Dark Theme',
                ThemeMode.dark,
                Icons.dark_mode,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    String title,
    ThemeMode mode,
    IconData icon,
  ) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: themeProvider.themeMode == mode
              ? colorScheme
                    .primary // Use theme color
              : colorScheme.surfaceContainerHighest, // Use theme color
          width: 2,
        ),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: themeProvider.themeMode == mode
              ? colorScheme
                    .primary // Use theme color
              : colorScheme.onSurfaceVariant, // Use theme color
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: themeProvider.themeMode == mode
                ? colorScheme
                      .primary // Use theme color
                : colorScheme.onSurface, // Use theme color
          ),
        ),
        trailing: themeProvider.themeMode == mode
            ? Icon(
                Icons.check_circle,
                color: colorScheme.primary,
              ) // Use theme color
            : null,
        onTap: () {
          themeProvider.setThemeMode(mode);
          Navigator.pop(context);
        },
      ),
    );
  }

  void _navigateToTermsOfService(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Terms of Service page coming soon!'),
        backgroundColor: Theme.of(
          context,
        ).colorScheme.primary, // Use theme color
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _deleteAccount(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Delete Account',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.error, // Use theme color
            ),
          ),
          content: Text(
            'Are you sure you want to delete your account? This action cannot be undone.',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ), // Use theme color
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(
                  context,
                ).colorScheme.onSurfaceVariant, // Use theme color
              ),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(
                      'Account deletion functionality coming soon!',
                    ),
                    backgroundColor: Theme.of(
                      context,
                    ).colorScheme.error, // Use theme color
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    margin: const EdgeInsets.all(16),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(
                  context,
                ).colorScheme.error, // Use theme color
                foregroundColor: Theme.of(
                  context,
                ).colorScheme.onError, // Use theme color
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final User? user = FirebaseAuth.instance.currentUser;
    final userName = user?.email?.split('@')[0] ?? 'User';
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Scaffold(
      backgroundColor: colorScheme.surface, // Use theme color
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(top: 60, bottom: 40),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary,
                    colorScheme.primary.withValues(alpha: 0.8),
                  ], // Use theme color
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: [
                    // Profile Avatar
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: colorScheme.surface, // Use theme color
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color:
                                cardTheme.shadowColor ??
                                Colors.black.withValues(
                                  alpha: 0.1,
                                ), // Use theme shadow color
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: CircleAvatar(
                        radius: 50,
                        backgroundColor: colorScheme.primary.withValues(
                          alpha: 0.1,
                        ), // Use theme color
                        child: Text(
                          userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary, // Use theme color
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // User Name
                    Text(
                      userName,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onPrimary, // Use theme color
                      ),
                    ),
                    const SizedBox(height: 4),
                    // User Email
                    Text(
                      user?.email ?? '<EMAIL>',
                      style: TextStyle(
                        fontSize: 16,
                        color: colorScheme.onPrimary.withValues(
                          alpha: 0.9,
                        ), // Use theme color
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Content Section
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Account Information Card
                  _buildSectionCard(
                    context: context, // Pass context
                    title: 'Account Information',
                    icon: Icons.person_outline,
                    children: [
                      _buildInfoTile(
                        context: context, // Pass context
                        icon: Icons.email_outlined,
                        title: 'Email Address',
                        subtitle: user?.email ?? 'Not available',
                      ),
                      _buildInfoTile(
                        context: context, // Pass context
                        icon: Icons.verified_user_outlined,
                        title: 'Account Status',
                        subtitle: user?.emailVerified == true
                            ? 'Verified'
                            : 'Unverified',
                        subtitleColor: user?.emailVerified == true
                            ? AppColors
                                  .success // Use AppColors for specific success/error
                            : AppColors
                                  .error, // Use AppColors for specific success/error
                      ),
                      // Removed "Member Since"
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Settings Card
                  _buildSectionCard(
                    context: context, // Pass context
                    title: 'Settings',
                    icon: Icons.settings_outlined,
                    children: [
                      _buildActionTile(
                        context: context, // Pass context
                        icon: Icons.palette_outlined,
                        title: 'Theme',
                        subtitle: 'Customize app appearance',
                        onTap: () => _showThemeSelection(context),
                      ),
                      _buildActionTile(
                        context: context, // Pass context
                        icon: Icons.notifications_outlined,
                        title: 'Notifications',
                        subtitle: 'Manage notification preferences',
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Text(
                                'Notification settings coming soon!',
                              ),
                              backgroundColor:
                                  colorScheme.primary, // Use theme color
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              margin: const EdgeInsets.all(16),
                            ),
                          );
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Support Card
                  _buildSectionCard(
                    context: context, // Pass context
                    title: 'Support',
                    icon: Icons.help_outline,
                    children: [
                      _buildActionTile(
                        context: context, // Pass context
                        icon: Icons.description_outlined,
                        title: 'Terms of Service',
                        subtitle: 'Read our terms and conditions',
                        onTap: () => _navigateToTermsOfService(context),
                      ),
                      _buildActionTile(
                        context: context, // Pass context
                        icon: Icons.privacy_tip_outlined,
                        title: 'Privacy Policy',
                        subtitle: 'Learn about data protection',
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Text(
                                'Privacy Policy page coming soon!',
                              ),
                              backgroundColor:
                                  colorScheme.primary, // Use theme color
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              margin: const EdgeInsets.all(16),
                            ),
                          );
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Danger Zone Card
                  _buildSectionCard(
                    context: context, // Pass context
                    title: 'Danger Zone',
                    icon: Icons.warning_outlined,
                    titleColor: colorScheme.error, // Use theme color
                    children: [
                      _buildActionTile(
                        context: context, // Pass context
                        icon: Icons.delete_forever_outlined,
                        title: 'Delete Account',
                        subtitle: 'Permanently delete your account',
                        onTap: () => _deleteAccount(context),
                        isDestructive: true,
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // Sign Out Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _signOut(context),
                      icon: const Icon(Icons.logout),
                      label: const Text(
                        'Sign Out',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.error, // Use theme color
                        foregroundColor: colorScheme.onError, // Use theme color
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required BuildContext context, // Added context
    required String title,
    required IconData icon,
    required List<Widget> children,
    Color? titleColor,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface, // Use theme color
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color:
                cardTheme.shadowColor ??
                Colors.black.withValues(alpha: 0.05), // Use theme shadow color
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: titleColor ?? colorScheme.primary, // Use theme color
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color:
                        titleColor ?? colorScheme.onSurface, // Use theme color
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoTile({
    required BuildContext context, // Added context
    required IconData icon,
    required String title,
    required String subtitle,
    Color? subtitleColor,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(
                alpha: 0.1,
              ), // Use theme color
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: colorScheme.primary, // Use theme color
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface, // Use theme color
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 16,
                    color:
                        subtitleColor ??
                        colorScheme.onSurfaceVariant, // Use theme color
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required BuildContext context, // Added context
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isDestructive
                    ? colorScheme.error.withValues(
                        alpha: 0.1,
                      ) // Use theme color
                    : colorScheme.primary.withValues(
                        alpha: 0.1,
                      ), // Use theme color
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isDestructive
                    ? colorScheme
                          .error // Use theme color
                    : colorScheme.primary, // Use theme color
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDestructive
                          ? colorScheme
                                .error // Use theme color
                          : colorScheme.onSurface, // Use theme color
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: colorScheme.onSurfaceVariant, // Use theme color
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: isDestructive
                  ? colorScheme
                        .error // Use theme color
                  : colorScheme.onSurfaceVariant, // Use theme color
            ),
          ],
        ),
      ),
    );
  }
}
