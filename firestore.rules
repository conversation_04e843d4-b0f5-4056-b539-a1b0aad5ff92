rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      // Allow authenticated users to read their own user document
      allow read: if request.auth != null && request.auth.uid == userId;

      match /subscriptions/{subscriptionId} {
        // Allow authenticated users to create their own subscriptions
        allow create: if request.auth != null && request.auth.uid == userId &&
                      request.resource.data.name is string &&
                      request.resource.data.amount is number &&
                      request.resource.data.category is string &&
                      request.resource.data.billingCycle is string &&
                      request.resource.data.expiryDate is timestamp &&
                      request.resource.data.nextBillingDate is timestamp &&
                      request.resource.data.isActive is bool &&
                      request.resource.data.createdAt is timestamp;

        // Allow authenticated users to read their own subscriptions
        allow read: if request.auth != null && request.auth.uid == userId;

        // Allow authenticated users to update their own subscriptions
        allow update: if request.auth != null && request.auth.uid == userId &&
                        request.resource.data.name is string &&
                        request.resource.data.amount is number &&
                        request.resource.data.category is string &&
                        request.resource.data.billingCycle is string &&
                        request.resource.data.expiryDate is timestamp &&
                        request.resource.data.nextBillingDate is timestamp &&
                        request.resource.data.isActive is bool &&
                        request.resource.data.createdAt is timestamp; // createdAt should not be changed after creation

        // Allow authenticated users to delete their own subscriptions
        allow delete: if request.auth != null && request.auth.uid == userId;
      }
    }
  }
}
