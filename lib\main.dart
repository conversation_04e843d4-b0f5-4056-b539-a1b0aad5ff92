import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart'; // Import FirebaseAuth
import 'package:provider/provider.dart'; // Import provider
import './screens/auth/sign_in.dart';
import './widgets/navigation.dart';
import './theme/app_theme.dart'; // Import AppTheme
import './theme/theme_manager.dart'; // Import ThemeProvider

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(); // Ensure Firebase is initialized here
  runApp(
    ChangeNotifierProvider(
      create: (context) => ThemeProvider(),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'Subscription Reminder',
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme, // Apply light theme
          darkTheme: AppTheme.darkTheme, // Apply dark theme
          themeMode: themeProvider.themeMode, // Use theme mode from provider
          home:
              const AuthWrapper(), // Directly use AuthWrapper after Firebase init in main()
        );
      },
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme; // Get colorScheme
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            body: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  colorScheme.primary,
                ), // Use theme color
              ),
            ),
          );
        }
        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Text('Error: ${snapshot.error}'), // Display error if any
            ),
          );
        }
        if (snapshot.hasData) {
          return const MainNavigation();
        }
        return const SignInScreen();
      },
    );
  }
}
