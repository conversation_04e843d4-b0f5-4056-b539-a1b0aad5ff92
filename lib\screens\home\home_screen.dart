import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'package:notrail/screens/history/history_screen.dart';
import 'package:notrail/theme/app_theme.dart'; // Import AppTheme for AppColors

// ─────────────────────────────
// Subscription Data Model
// ─────────────────────────────
class Subscription {
  final String id;
  final String name;
  final double amount;
  final String category;
  final String billingCycle;
  final DateTime nextBillingDate;
  final bool isActive;
  final DateTime createdAt;

  Subscription({
    required this.id,
    required this.name,
    required this.amount,
    required this.category,
    required this.billingCycle,
    required this.nextBillingDate,
    required this.isActive,
    required this.createdAt,
  });

  int get daysUntilNextBilling {
    final now = DateTime.now();
    final difference = nextBillingDate.difference(now);
    return difference.inDays + (difference.isNegative ? 0 : 1);
  }

  factory Subscription.fromFirestore(DocumentSnapshot doc) {
    Map data = doc.data() as Map<String, dynamic>;
    return Subscription(
      id: doc.id,
      name: data['name'] ?? '',
      amount: (data['amount'] ?? 0.0).toDouble(),
      category: data['category'] ?? 'Other',
      billingCycle: data['billingCycle'] ?? 'Monthly',
      nextBillingDate: (data['nextBillingDate'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'amount': amount,
      'category': category,
      'billingCycle': billingCycle,
      'nextBillingDate': Timestamp.fromDate(nextBillingDate),
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

// ─────────────────────────────
// Enhanced Home Screen Widget
// ─────────────────────────────
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  User? get currentUser => _auth.currentUser;

  Stream<List<Subscription>> _getSubscriptionStream() {
    if (currentUser == null) {
      return Stream.value([]);
    }
    return _firestore
        .collection('users')
        .doc(currentUser!.uid)
        .collection('subscriptions')
        .where('isActive', isEqualTo: true) // Only fetch active subscriptions
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => Subscription.fromFirestore(doc))
              .toList();
        });
  }

  Future<void> cancelSubscription(String subscriptionId) async {
    if (currentUser == null) return;
    await _firestore
        .collection('users')
        .doc(currentUser!.uid)
        .collection('subscriptions')
        .doc(subscriptionId)
        .update({
          'isActive': false, // Set isActive to false instead of isCanceled
          'canceledAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
  }

  // Helper to get category icon
  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName) {
      case 'Entertainment':
        return Icons.movie;
      case 'Streaming':
        return Icons.play_circle_filled;
      case 'Music':
        return Icons.music_note;
      case 'Software':
        return Icons.computer;
      case 'Fitness':
        return Icons.fitness_center;
      case 'News':
        return Icons.newspaper;
      case 'Finance':
        return Icons.account_balance;
      case 'Productivity':
        return Icons.business;
      case 'Education':
        return Icons.school;
      case 'Food & Dining':
        return Icons.restaurant;
      case 'Health':
        return Icons.local_hospital;
      case 'Other':
        return Icons.category;
      default:
        return Icons.subscriptions_outlined; // Default icon
    }
  }

  // Helper to get category color (optional, but good for consistency)
  Color _getCategoryColor(String categoryName) {
    switch (categoryName) {
      case 'Entertainment':
        return AppColors.categoryEntertainment;
      case 'Streaming':
        return AppColors.categoryStreaming;
      case 'Music':
        return AppColors.categoryMusic;
      case 'Software':
        return AppColors.categorySoftware;
      case 'Fitness':
        return AppColors.categoryFitness;
      case 'News':
        return AppColors.categoryNews;
      case 'Finance':
        return AppColors.categoryFinance;
      case 'Productivity':
        return AppColors.categoryProductivity;
      case 'Education':
        return AppColors.categoryEducation;
      case 'Food & Dining':
        return AppColors.categoryFoodDining;
      case 'Health':
        return AppColors.categoryHealth;
      case 'Other':
        return AppColors.categoryOther;
      default:
        return Theme.of(context).colorScheme.primary; // Default color
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor, // Use theme color
      appBar: AppBar(automaticallyImplyLeading: false,
        title: const Text(
          'Subscription Manager',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        centerTitle: true,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor, // Use theme color
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor, // Use theme color
        elevation: 0,
        shadowColor: cardTheme.shadowColor, // Use theme shadow color
      ),
      body: currentUser == null
          ? _buildSignInPrompt(context) // Pass context
          : StreamBuilder<List<Subscription>>(
              stream: _getSubscriptionStream(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        colorScheme.primary,
                      ), // Use theme color
                    ),
                  );
                }
                if (snapshot.hasError) {
                  return _buildErrorView(
                    context,
                    snapshot.error.toString(),
                  ); // Pass context
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return _buildNoSubscriptionsView(context);
                }

                return _buildMainContent(
                  context,
                  snapshot.data!,
                ); // Pass context
              },
            ),
    );
  }

  Widget _buildSignInPrompt(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 80,
            color: colorScheme.onSurfaceVariant, // Use theme color
          ),
          const SizedBox(height: 24),
          Text(
            'Please sign in to continue',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: colorScheme.onSurfaceVariant, // Use theme color
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Access your subscription management dashboard',
            style: TextStyle(
              fontSize: 14,
              color: colorScheme.onSurfaceVariant, // Use theme color
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String error) {
    final colorScheme = Theme.of(context).colorScheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: colorScheme.error, // Use theme color
          ),
          const SizedBox(height: 24),
          Text(
            'Something went wrong',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: colorScheme.onSurface, // Use theme color
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 14,
              color: colorScheme.onSurfaceVariant, // Use theme color
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoSubscriptionsView(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.subscriptions_outlined,
            size: 80,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 24),
          Text(
            'No subscriptions yet!',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first subscription to get started.',
            style: TextStyle(fontSize: 14, color: colorScheme.onSurfaceVariant),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    List<Subscription> subscriptions,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme; // Re-added cardTheme
    final double totalSaved = subscriptions
        .where((sub) => !sub.isActive) // Check isActive for canceled
        .fold(0.0, (currentSum, sub) => currentSum + sub.amount);
    final int totalSubscriptionsCount = subscriptions.length;
    final int canceledSubscriptionsCount = subscriptions
        .where((sub) => !sub.isActive)
        .length; // Check isActive for canceled
    final double totalMonthlySpending = subscriptions
        .where((sub) => sub.isActive) // Only active subscriptions
        .fold(0.0, (currentSum, sub) => currentSum + sub.amount);
    final double averageSubscriptionCost = totalSubscriptionsCount > 0
        ? totalMonthlySpending /
              (totalSubscriptionsCount - canceledSubscriptionsCount)
        : 0.0;

    final List<Subscription> activeSubscriptions =
        subscriptions.where((sub) => sub.isActive).toList()
          ..sort((a, b) => a.name.compareTo(b.name));

    final List<Subscription> upcomingTrials = subscriptions.where((sub) {
      final now = DateTime.now();
      return sub.isActive &&
          sub.nextBillingDate.isAfter(now.subtract(const Duration(days: 1))) &&
          sub.nextBillingDate.difference(now).inDays <= 7;
    }).toList()..sort((a, b) => a.nextBillingDate.compareTo(b.nextBillingDate));

    return RefreshIndicator(
      onRefresh: () async {
        setState(() {});
      },
      color: colorScheme.primary, // Use theme color
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(context), // Pass context
            const SizedBox(height: 32),

            // Summary Cards
            _buildSummaryCards(
              context, // Pass context
              totalSubscriptionsCount,
              canceledSubscriptionsCount,
              totalSaved,
              totalMonthlySpending,
              averageSubscriptionCost,
              cardTheme, // Pass cardTheme
            ),
            const SizedBox(height: 32),

            // Upcoming Expirations Alert
            if (upcomingTrials.isNotEmpty) ...[
              _buildExpirationAlert(context, upcomingTrials), // Pass context
              const SizedBox(height: 32),
            ],

            // Active Subscriptions
            _buildActiveSubscriptionsSection(
              context,
              activeSubscriptions,
            ), // Pass context
            const SizedBox(height: 32),

            // Action Buttons
            _buildActionButtons(context), // Pass context
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    final User? currentUser = _auth.currentUser; // Get current user here
    final userName = currentUser?.email?.split('@')[0] ?? 'User';
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.surface, // Use theme surface color
            colorScheme.surface.withOpacity(0.7), // Use theme surface color
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: cardTheme.shadowColor ?? Colors.black.withOpacity(0.1), // Use theme shadow color
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back,',
                  style: TextStyle(
                    color: colorScheme.onSurface, // Use theme onSurface color
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  userName,
                  style: TextStyle(
                    color: colorScheme.onSurface, // Use theme onSurface color
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Manage your subscriptions with ease',
                  style: TextStyle(
                    color: colorScheme.onSurfaceVariant, // Use theme onSurfaceVariant color
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest, // Use theme surfaceContainerHighest color
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              Icons.person_outline,
              color: colorScheme.onSurface, // Use theme onSurface color
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(
    BuildContext context, // Pass context
    int totalSubs,
    int canceledSubs,
    double totalSaved,
    double monthlySpend,
    double avgCost,
    CardThemeData cardTheme, // Changed from CardTheme to CardThemeData
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      children: [
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildSummaryCard(
              context,
              'Total Subscriptions',
              totalSubs.toString(),
              Icons.subscriptions_outlined,
              colorScheme.primary,
            ),
            _buildSummaryCard(
              context,
              'Canceled',
              canceledSubs.toString(),
              Icons.cancel_outlined,
              colorScheme.error,
            ),
            _buildSummaryCard(
              context,
              'Money Saved',
              'R${totalSaved.toStringAsFixed(0)}',
              Icons.savings_outlined,
              colorScheme.tertiary,
            ),
            _buildSummaryCard(
              context,
              'Monthly Spend',
              'R${monthlySpend.toStringAsFixed(0)}',
              Icons.account_balance_wallet_outlined,
              colorScheme.primary,
            ),
            _buildSummaryCard(
              context,
              'Avg. Cost',
              'R${avgCost.toStringAsFixed(0)}',
              Icons.attach_money_outlined,
              colorScheme.secondary,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color iconColor,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Card(
      elevation: cardTheme.elevation,
      shape: cardTheme.shape,
      color: cardTheme.color,
      shadowColor: cardTheme.shadowColor,
      surfaceTintColor: cardTheme.surfaceTintColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Icon(
              icon,
              color: iconColor,
              size: 32,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpirationAlert(
    BuildContext context,
    List<Subscription> upcomingTrials,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.tertiaryContainer,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: cardTheme.shadowColor ?? Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber_outlined,
                color: colorScheme.onTertiaryContainer,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Upcoming Expirations',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: colorScheme.onTertiaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...upcomingTrials.map((sub) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: colorScheme.onTertiaryContainer.withOpacity(0.8),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${sub.name} expires in ${sub.daysUntilNextBilling} days (${DateFormat('MMM dd, yyyy').format(sub.nextBillingDate)})',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onTertiaryContainer,
                          ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildActiveSubscriptionsSection(
    BuildContext context,
    List<Subscription> activeSubscriptions,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Active Subscriptions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        if (activeSubscriptions.isEmpty)
          Center(
            child: Text(
              'No active subscriptions.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activeSubscriptions.length,
            itemBuilder: (context, index) {
              final sub = activeSubscriptions[index];
              return Card(
                elevation: cardTheme.elevation,
                shape: cardTheme.shape,
                color: cardTheme.color,
                shadowColor: cardTheme.shadowColor,
                surfaceTintColor: cardTheme.surfaceTintColor,
                margin: const EdgeInsets.only(bottom: 12),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _getCategoryIcon(sub.category),
                            color: _getCategoryColor(sub.category),
                            size: 28,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  sub.name,
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        color: colorScheme.onSurface,
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                                Text(
                                  sub.category,
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelMedium
                                      ?.copyWith(
                                        color: colorScheme.onSurfaceVariant,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            'R${sub.amount.toStringAsFixed(0)}',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  color: colorScheme.onSurface,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Next Billing:',
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                              ),
                              Text(
                                DateFormat('MMM dd, yyyy')
                                    .format(sub.nextBillingDate),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: colorScheme.onSurface,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                'Billing Cycle:',
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                              ),
                              Text(
                                sub.billingCycle,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: colorScheme.onSurface,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Viewing details for ${sub.name} coming soon!',
                                  ),
                                  backgroundColor:
                                      colorScheme.primary, // Use theme color
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  margin: const EdgeInsets.all(16),
                                ),
                              );
                            },
                            child: const Text('View Details'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () => cancelSubscription(sub.id),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: colorScheme.error, // Use theme color
                              foregroundColor: colorScheme.onError, // Use theme color
                            ),
                            child: const Text('Cancel'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const HistoryScreen()),
              );
            },
            icon: const Icon(Icons.history),
            label: const Text('History'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.secondary, // Use theme color
              foregroundColor: colorScheme.onSecondary, // Use theme color
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Add New Subscription coming soon!'),
                  backgroundColor: colorScheme.primary, // Use theme color
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  margin: const EdgeInsets.all(16),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('Add New'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primary, // Use theme color
              foregroundColor: colorScheme.onPrimary, // Use theme color
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ),
      ],
    );
  }
}
