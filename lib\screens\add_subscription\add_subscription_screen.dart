import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'package:notrail/theme/app_theme.dart'; // Import AppTheme for AppColors
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';

class AddSubscriptionScreen extends StatefulWidget {
  const AddSubscriptionScreen({super.key});

  @override
  State<AddSubscriptionScreen> createState() => _AddSubscriptionScreenState();
}

class _AddSubscriptionScreenState extends State<AddSubscriptionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();

  // Controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _customCategoryController =
      TextEditingController();

  // State variables
  DateTime? _selectedExpiryDate;
  String? _selectedCategory;
  String _billingCycle = 'Monthly';
  int _currentStep = 0;
  bool _isLoading = false;
  bool _showCustomCategory = false;
  Map<String, int> _customCategoryColors =
      {}; // Stores custom category colors (category name -> ARGB int)

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  User? get currentUser => _auth.currentUser;

  // Predefined categories with icons and colors
  // Using a map to easily retrieve color by name
  final Map<String, Map<String, dynamic>> _categoriesMap = {
    'Entertainment': {
      'icon': Icons.movie,
      'color': AppColors.categoryEntertainment,
    },
    'Streaming': {
      'icon': Icons.play_circle_filled,
      'color': AppColors.categoryStreaming,
    },
    'Music': {'icon': Icons.music_note, 'color': AppColors.categoryMusic},
    'Software': {'icon': Icons.computer, 'color': AppColors.categorySoftware},
    'Fitness': {
      'icon': Icons.fitness_center,
      'color': AppColors.categoryFitness,
    },
    'News': {'icon': Icons.newspaper, 'color': AppColors.categoryNews},
    'Finance': {
      'icon': Icons.account_balance,
      'color': AppColors.categoryFinance,
    },
    'Productivity': {
      'icon': Icons.business,
      'color': AppColors.categoryProductivity,
    },
    'Education': {'icon': Icons.school, 'color': AppColors.categoryEducation},
    'Food & Dining': {
      'icon': Icons.restaurant,
      'color': AppColors.categoryFoodDining,
    },
    'Health': {'icon': Icons.local_hospital, 'color': AppColors.categoryHealth},
    'Other': {'icon': Icons.category, 'color': AppColors.categoryOther},
  };

  // A list of visually appealing colors for dynamic assignment
  final List<Color> _availableColors = [
    Colors.red.shade400,
    Colors.blue.shade400,
    Colors.green.shade400,
    Colors.purple.shade400,
    Colors.orange.shade400,
    Colors.teal.shade400,
    Colors.pink.shade400,
    Colors.indigo.shade400,
    Colors.amber.shade400,
    Colors.cyan.shade400,
  ];

  @override
  void initState() {
    super.initState();
    _loadCustomCategoryColors();
  }

  List<Map<String, dynamic>> get _categories {
    final List<Map<String, dynamic>> allCategories = _categoriesMap.entries
        .map((e) => {'name': e.key, ...e.value})
        .toList();

    // Add custom categories with their loaded colors
    _customCategoryColors.forEach((name, colorValue) {
      // Only add if it's not already a predefined category
      if (!_categoriesMap.containsKey(name)) {
        allCategories.add({
          'name': name,
          'icon': Icons.category,
          'color': Color(colorValue),
        });
      }
    });
    return allCategories;
  }

  final List<String> _billingCycles = [
    'Weekly',
    'Monthly',
    'Quarterly',
    'Yearly',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _customCategoryController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomCategoryColors() async {
    final prefs = await SharedPreferences.getInstance();
    final customColorsJson = prefs.getString('customCategoryColors');
    if (customColorsJson != null) {
      setState(() {
        _customCategoryColors = Map<String, int>.from(
          (json.decode(customColorsJson) as Map).map(
            (key, value) => MapEntry(key, value as int),
          ),
        );
      });
    }
  }

  Future<void> _saveCustomCategoryColors() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'customCategoryColors',
      json.encode(_customCategoryColors),
    );
  }

  Color _generateUniqueColor() {
    // Simple cycling through available colors.
    // More sophisticated logic could be added here to ensure better uniqueness
    // or to avoid colors too similar to existing ones.
    final usedColors = _categories.map((e) => e['color'] as Color).toList();
    for (Color color in _availableColors) {
      if (!usedColors.contains(color)) {
        return color;
      }
    }
    // If all predefined colors are used, generate a random one (less ideal)
    return Color((Random().nextDouble() * 0xFFFFFF).toInt()).withOpacity(1.0);
  }

  Future<void> _selectDate(BuildContext context) async {
    final colorScheme = Theme.of(context).colorScheme;
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedExpiryDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: colorScheme, // Use current theme's colorScheme
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedExpiryDate) {
      setState(() {
        _selectedExpiryDate = picked;
      });
    }
  }

  void _nextStep() {
    if (_currentStep < 2) {
      if (_validateCurrentStep()) {
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else {
      _addSubscription();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _nameController.text.isNotEmpty;
      case 1:
        return _selectedCategory != null ||
            _customCategoryController.text.isNotEmpty;
      case 2:
        return _amountController.text.isNotEmpty &&
            double.tryParse(_amountController.text) != null &&
            _selectedExpiryDate != null;
      default:
        return true;
    }
  }

  Future<void> _addSubscription() async {
    if (!_formKey.currentState!.validate() || !_validateCurrentStep()) {
      return;
    }

    if (currentUser == null) {
      _showSnackBar('Please sign in to add subscriptions.', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final categoryToSave = _showCustomCategory
          ? _customCategoryController.text.trim()
          : _selectedCategory!;

      Color categoryColor;
      if (_showCustomCategory) {
        if (_customCategoryColors.containsKey(categoryToSave)) {
          categoryColor = Color(_customCategoryColors[categoryToSave]!);
        } else {
          categoryColor = _generateUniqueColor();
          _customCategoryColors[categoryToSave] = categoryColor.value;
          await _saveCustomCategoryColors(); // Save the new custom color
        }
      } else {
        categoryColor = _categoriesMap[categoryToSave]!['color'] as Color;
      }

      await _firestore
          .collection('users')
          .doc(currentUser!.uid)
          .collection('subscriptions')
          .add({
            'name': _nameController.text.trim(),
            'amount': double.parse(_amountController.text),
            'category': categoryToSave,
            'billingCycle': _billingCycle,
            'expiryDate': Timestamp.fromDate(_selectedExpiryDate!),
            'nextBillingDate': _calculateNextBillingDate(),
            'isActive': true,
            'createdAt': FieldValue.serverTimestamp(),
            'categoryColor': categoryColor.value, // Save color as ARGB integer
          });

      if (mounted) {
        _showSnackBar('Subscription added successfully!', isError: false);
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(
          'Failed to add subscription: ${e.toString()}',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Timestamp _calculateNextBillingDate() {
    DateTime nextDate = DateTime.now();
    switch (_billingCycle) {
      case 'Weekly':
        nextDate = nextDate.add(const Duration(days: 7));
        break;
      case 'Monthly':
        nextDate = DateTime(nextDate.year, nextDate.month + 1, nextDate.day);
        break;
      case 'Quarterly':
        nextDate = DateTime(nextDate.year, nextDate.month + 3, nextDate.day);
        break;
      case 'Yearly':
        nextDate = DateTime(nextDate.year + 1, nextDate.month, nextDate.day);
        break;
    }
    return Timestamp.fromDate(nextDate);
  }

  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? Theme.of(context).colorScheme.error
            : AppColors.success, // Use theme color
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Scaffold(
      backgroundColor: colorScheme.surface, // Use theme color
      appBar: AppBar(
        title: Text(
          'Add Subscription',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface, // Use theme color
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Progress Indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              children: List.generate(3, (index) {
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(right: index < 2 ? 8 : 0),
                    height: 4,
                    decoration: BoxDecoration(
                      color: index <= _currentStep
                          ? colorScheme
                                .primary // Use theme color
                          : colorScheme
                                .surfaceContainerHighest, // Use theme color
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                );
              }),
            ),
          ),

          // Form Content
          Expanded(
            child: Form(
              key: _formKey,
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                },
                children: [
                  _buildBasicInfoStep(context),
                  _buildCategoryStep(context),
                  _buildPaymentDetailsStep(context),
                ],
              ),
            ),
          ),

          // Navigation Buttons
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            decoration: BoxDecoration(
              color: colorScheme.surface, // Use theme color
              boxShadow: [
                BoxShadow(
                  color:
                      cardTheme.shadowColor ??
                      Colors.black.withOpacity(0.05), // Use theme shadow color
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousStep,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: BorderSide(
                          color: colorScheme.surfaceContainerHighest,
                        ), // Use theme color
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Back',
                        style: TextStyle(
                          color:
                              colorScheme.onSurfaceVariant, // Use theme color
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  flex: _currentStep > 0 ? 1 : 1,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _nextStep,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary, // Use theme color
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: _isLoading
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                colorScheme.onPrimary,
                              ), // Use theme color
                            ),
                          )
                        : Text(
                            _currentStep < 2 ? 'Next' : 'Add Subscription',
                            style: TextStyle(
                              color: colorScheme.onPrimary, // Use theme color
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoStep(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface, // Use theme color
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Let\'s start with the subscription name',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onSurfaceVariant, // Use theme color
            ),
          ),
          const SizedBox(height: 32),
          _buildModernTextField(
            context: context, // Pass context
            controller: _nameController,
            label: 'Subscription Name',
            hint: 'e.g., Netflix, Spotify, Adobe Creative Cloud',
            icon: Icons.label_outline,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a subscription name';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryStep(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Category',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface, // Use theme color
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a category to organize your subscriptions',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onSurfaceVariant, // Use theme color
            ),
          ),
          const SizedBox(height: 32),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 3,
            ),
            itemCount: _categories.length,
            itemBuilder: (context, index) {
              final category = _categories[index];
              final isSelected = _selectedCategory == category['name'];
              final categoryColor = category['color'] as Color;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory = category['name'];
                    _showCustomCategory = category['name'] == 'Other';
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected
                        ? categoryColor
                        : colorScheme.surface, // Use theme color
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? categoryColor
                          : colorScheme
                                .surfaceContainerHighest, // Use theme color
                      width: 2,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: categoryColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        category['icon'],
                        color: isSelected
                            ? colorScheme.onPrimary
                            : categoryColor, // Use theme color
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          category['name'],
                          style: TextStyle(
                            color: isSelected
                                ? colorScheme.onPrimary
                                : colorScheme.onSurface, // Use theme color
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          if (_showCustomCategory) ...[
            const SizedBox(height: 24),
            _buildModernTextField(
              context: context, // Pass context
              controller: _customCategoryController,
              label: 'Custom Category',
              hint: 'Enter your custom category',
              icon: Icons.edit_outlined,
              validator: (value) {
                if (_showCustomCategory &&
                    (value == null || value.trim().isEmpty)) {
                  return 'Please enter a custom category name';
                }
                return null;
              },
            ),
          ],
          const SizedBox(height: 24),
          Text(
            'Billing Cycle',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface, // Use theme color
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _billingCycles.map((cycle) {
              final isSelected = _billingCycle == cycle;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _billingCycle = cycle;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme
                              .surfaceContainerHighest, // Use theme color
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? colorScheme.primary
                          : Colors.transparent, // Use theme color
                      width: 2,
                    ),
                  ),
                  child: Text(
                    cycle,
                    style: TextStyle(
                      color: isSelected
                          ? colorScheme.onPrimary
                          : colorScheme.onSurfaceVariant, // Use theme color
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetailsStep(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Details',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface, // Use theme color
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter the amount and next billing date',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onSurfaceVariant, // Use theme color
            ),
          ),
          const SizedBox(height: 32),
          _buildModernTextField(
            context: context, // Pass context
            controller: _amountController,
            label: 'Amount',
            hint: 'e.g., 9.99',
            icon: Icons.attach_money,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter an amount';
              }
              if (double.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
          Text(
            'Next Billing Date',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface, // Use theme color
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () => _selectDate(context),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest, // Use theme color
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ), // Use theme color
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: colorScheme.onSurfaceVariant,
                  ), // Use theme color
                  const SizedBox(width: 16),
                  Text(
                    _selectedExpiryDate == null
                        ? 'Select Date'
                        : DateFormat(
                            'dd MMMM yyyy',
                          ).format(_selectedExpiryDate!),
                    style: TextStyle(
                      fontSize: 16,
                      color: _selectedExpiryDate == null
                          ? colorScheme.onSurfaceVariant
                          : colorScheme.onSurface, // Use theme color
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ), // Use theme color
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      style: TextStyle(color: colorScheme.onSurface), // Use theme color
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        hintStyle: TextStyle(
          color: colorScheme.onSurfaceVariant,
        ), // Use theme color
        prefixIcon: Icon(
          icon,
          color: colorScheme.onSurfaceVariant,
        ), // Use theme color
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest, // Use theme color
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outlineVariant,
          ), // Use theme color
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ), // Use theme color
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.error,
            width: 2,
          ), // Use theme color
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.error,
            width: 2,
          ), // Use theme color
        ),
      ),
      validator: validator,
    );
  }
}
