rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Rule for the 'restaurants' collection where each restaurant's profile
    // is stored with the document ID matching the owner's UID.
    match /restaurants/{restaurantId} {
      // Allow read and write if the authenticated user's UID matches the document ID
      allow read, write: if request.auth != null && request.auth.uid == restaurantId;

      // Enhanced create validation to ensure required fields are present
      allow create: if request.auth != null &&
        request.auth.uid == restaurantId &&
        request.resource.data.name is string &&
        request.resource.data.name.size() > 0 &&
        request.resource.data.ownerName is string &&
        request.resource.data.ownerName.size() > 0 &&
        request.resource.data.email is string &&
        request.resource.data.email.size() > 0 &&
        request.resource.data.phone is string &&
        request.resource.data.phone.size() > 0 &&
        request.resource.data.address is string &&
        request.resource.data.address.size() > 0 &&
        request.resource.data.province is string &&
        request.resource.data.restaurantType is string &&
        request.resource.data.cuisineTypes is list;
      
      // Videos subcollection for analytics
      match /videos/{videoId} {
        allow read, write: if request.auth != null && request.auth.uid == restaurantId;
        
        // **UPDATED** Validation for video creation to include new fields
        allow create: if request.auth != null &&
          request.auth.uid == restaurantId &&
          request.resource.data.title is string &&
          request.resource.data.title.size() > 0 &&
          request.resource.data.views is number &&
          request.resource.data.sales is number &&
          request.resource.data.createdAt is timestamp &&
          // New fields added to the rule:
          request.resource.data.description is string &&
          request.resource.data.description.size() > 0 && // Assuming description is required
          request.resource.data.category is string &&
          request.resource.data.category.size() > 0 &&  // Assuming category is required
          request.resource.data.isActive is bool &&
          request.resource.data.videoFileName is string &&
          request.resource.data.videoFileName.size() > 0 &&
          // Optional fields: check if they exist AND are of the correct type
          (!request.resource.data.keys().hasAny(['price']) || request.resource.data.price is number) &&
          (!request.resource.data.keys().hasAny(['prepTime']) || request.resource.data.prepTime is number);


        // Allow updates to views and sales for analytics tracking
        allow update: if request.auth != null &&
          request.auth.uid == restaurantId &&
          // Only allow updating views, sales, and trend fields
          request.resource.data.diff(resource.data).affectedKeys()
            .hasOnly(['views', 'sales', 'trend', 'updatedAt']);
      }
      
      // Menu items subcollection with analytics data
      match /menu/{menuId} {
        allow read, write: if request.auth != null && request.auth.uid == restaurantId;
        
        // Validation for menu item creation
        allow create: if request.auth != null &&
          request.auth.uid == restaurantId &&
          request.resource.data.name is string &&
          request.resource.data.name.size() > 0 &&
          request.resource.data.price is number &&
          request.resource.data.category is string &&
          request.resource.data.createdAt is timestamp;
        
        // Allow updates to views and sales for analytics tracking
        allow update: if request.auth != null &&
          request.auth.uid == restaurantId &&
          // Allow updating analytics fields
          request.resource.data.diff(resource.data).affectedKeys()
            .hasOnly(['views', 'sales', 'trend', 'updatedAt', 'price', 'description', 'available']);
      }
      
      // Analytics data subcollection for storing aggregated metrics
      match /analytics/{analyticsId} {
        allow read, write: if request.auth != null && request.auth.uid == restaurantId;
        
        // Validation for analytics data
        allow create: if request.auth != null &&
          request.auth.uid == restaurantId &&
          request.resource.data.date is timestamp &&
          request.resource.data.totalViews is number &&
          request.resource.data.totalSales is number;
      }
    }
    // Optional: Allow public read access to restaurant listings (for customer app)
    // Uncomment if you need customers to browse restaurants
    /*
    match /restaurants/{restaurantId} {
      allow read: if true; // Public read access
      allow write: if request.auth != null && request.auth.uid == restaurantId;
    }
    */
    // Orders collection with enhanced analytics support
    match /orders/{orderId} {
      allow read, write: if request.auth != null &&
        (request.auth.uid == resource.data.restaurantId ||
         request.auth.uid == resource.data.customerId);
      
      // Validation for order creation
      allow create: if request.auth != null &&
        request.resource.data.restaurantId is string &&
        request.resource.data.customerId is string &&
        request.resource.data.items is list &&
        request.resource.data.totalAmount is number &&
        request.resource.data.status is string &&
        request.resource.data.createdAt is timestamp &&
        // Ensure the user is either the restaurant owner or customer
        (request.auth.uid == request.resource.data.restaurantId ||
         request.auth.uid == request.resource.data.customerId);
      
      // Allow status updates by restaurant owners
      allow update: if request.auth != null &&
        request.auth.uid == resource.data.restaurantId &&
        request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['status', 'updatedAt', 'estimatedDelivery']);
    }
    // Customer profiles (optional for analytics)
    match /customers/{customerId} {
      allow read, write: if request.auth != null && request.auth.uid == customerId;
      
      // Validation for customer profile creation
      allow create: if request.auth != null &&
        request.auth.uid == customerId &&
        request.resource.data.name is string &&
        request.resource.data.email is string &&
        request.resource.data.createdAt is timestamp;
    }
    // Reviews collection for menu items and videos
    match /reviews/{reviewId} {
      // Allow read access to all authenticated users
      allow read: if request.auth != null;
      
      // Allow write access only to the review author
      allow write: if request.auth != null && request.auth.uid == resource.data.authorId;
      
      // Validation for review creation
      allow create: if request.auth != null &&
        request.resource.data.authorId == request.auth.uid &&
        request.resource.data.rating is number &&
        request.resource.data.rating >= 1 &&
        request.resource.data.rating <= 5 &&
        request.resource.data.comment is string &&
        request.resource.data.targetType is string &&
        request.resource.data.targetId is string &&
        request.resource.data.createdAt is timestamp;
      
      // Allow updates only by the author
      allow update: if request.auth != null &&
        request.auth.uid == resource.data.authorId &&
        request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['comment', 'rating', 'updatedAt']);
    }
    // Notifications collection
    match /notifications/{notificationId} {
      // Allow read and write only to notification recipient
      allow read, write: if request.auth != null && request.auth.uid == resource.data.recipientId;
      
      // Validation for notification creation
      allow create: if request.auth != null &&
        request.resource.data.recipientId is string &&
        request.resource.data.title is string &&
        request.resource.data.message is string &&
        request.resource.data.type is string &&
        request.resource.data.read == false &&
        request.resource.data.createdAt is timestamp;
      
      // Allow marking as read
      allow update: if request.auth != null &&
        request.auth.uid == resource.data.recipientId &&
        request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['read', 'readAt']);
    }
    // Admin collection (for app administrators)
    match /admin/{document=**} {
      // Only allow access to admin users (you can customize this logic)
      allow read, write: if request.auth != null &&
        request.auth.token.admin == true;
    }
    // Global settings collection
    match /settings/{settingId} {
      // Allow read access to all authenticated users
      allow read: if request.auth != null;
      
      // Only allow write access to admin users
      allow write: if request.auth != null &&
        request.auth.token.admin == true;
    }
  }
}